import mongoose from "mongoose";

const cartSchema = new mongoose.Schema(
  {
    // Add your Cart fields here
    name: {
      type: String,
      required: [true, "Cart name is required"],
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

const Cart = mongoose.model("Cart", cartSchema);
export default Cart;