#!/usr/bin/env node
import inquirer from "inquirer";
import chalk from "chalk";
import shell from "shelljs";
import figlet from "figlet";
import { spawnSync } from "child_process";
import ora from "ora";

import setupBackend from "./mern/backend/setupBackend.js";
import setupFrontend from "./mern/frontend/setupFrontend.js";
import {
  validateProjectName,
  checkPrerequisites,
  handleError,
  createProgressSpinner,
  createDirectory,
  executeCommand,
  writeFile,
} from "./utils/helpers.js";

console.log(chalk.green(figlet.textSync("StackWizard")));
console.log(
  chalk.cyan("🚀 Welcome to StackWizard - Your Project Setup Assistant\n")
);

async function main() {
  try {
    // Check prerequisites
    console.log(chalk.yellow("🔍 Checking prerequisites..."));
    const prereqCheck = checkPrerequisites();

    if (!prereqCheck.isValid) {
      console.log(chalk.red("❌ Missing required tools:"));
      prereqCheck.missing.forEach((tool) => {
        console.log(chalk.red(`   - ${tool}`));
      });
      console.log(
        chalk.yellow("\nPlease install the missing tools and try again.")
      );
      process.exit(1);
    }

    console.log(chalk.green("✅ All prerequisites satisfied!\n"));

    const answers = await inquirer.prompt([
      {
        type: "list",
        name: "stack",
        message: "Choose your stack:",
        choices: [
          {
            name: "🔥 MERN Stack (MongoDB, Express, React, Node.js)",
            value: "MERN",
          },
          { name: "⚡ Next.js (React Framework)", value: "Next.js" },
          { name: "🛠️ Other Frameworks", value: "Other" },
        ],
      },
    ]);

    console.log(chalk.blue(`\n✨ You selected: ${answers.stack}\n`));

    switch (answers.stack) {
      case "MERN":
        await setupMERN();
        break;
      case "Next.js":
        await setupNEXTJS();
        break;
      case "Other":
        await setupCustom();
        break;
      default:
        throw new Error("Invalid stack selection");
    }

    console.log(chalk.green("\n🎉 Project setup completed successfully!"));
    console.log(chalk.cyan("Happy coding! 🚀\n"));
  } catch (error) {
    handleError(error, "Main setup process");
  }
}

async function setupMERN() {
  try {
    const { projectName } = await inquirer.prompt([
      {
        type: "input",
        name: "projectName",
        message: "Enter your MERN project name:",
        default: "mern-project",
        validate: (input) => {
          const validation = validateProjectName(input);
          return validation.isValid || validation.error;
        },
      },
    ]);

    console.log(chalk.green(`\n📁 Creating project: ${projectName}\n`));

    // Step 1: Create project directory
    if (!createDirectory(projectName)) {
      throw new Error(`Failed to create project directory: ${projectName}`);
    }

    const originalDir = process.cwd();
    shell.cd(projectName);

    try {
      // Step 2: Setup Backend
      console.log(chalk.yellow("🔧 Setting up backend..."));
      await setupBackend();

      // Step 3: Setup Frontend
      console.log(chalk.yellow("🎨 Setting up frontend..."));
      await setupFrontend();

      // Step 4: Create root package.json for workspace
      console.log(chalk.yellow("📦 Creating workspace configuration..."));
      await createWorkspaceConfig(projectName);

      console.log(
        chalk.green(`\n✅ ${projectName} MERN stack setup completed! 🚀`)
      );
      console.log(chalk.cyan("\n📋 Next steps:"));
      console.log(chalk.white(`   1. cd ${projectName}`));
      console.log(chalk.white("   2. Start backend: cd server && npm run dev"));
      console.log(
        chalk.white("   3. Start frontend: cd client && npm run dev")
      );
    } catch (error) {
      shell.cd(originalDir);
      throw error;
    }

    shell.cd(originalDir);
  } catch (error) {
    handleError(error, "MERN stack setup");
  }
}

async function setupNEXTJS() {
  try {
    const { projectName } = await inquirer.prompt([
      {
        type: "input",
        name: "projectName",
        message: "Enter your Next.js project name:",
        default: "nextjs-app",
        validate: (input) => {
          const validation = validateProjectName(input);
          return validation.isValid || validation.error;
        },
      },
    ]);

    console.log(
      chalk.green(`\n⚡ Creating Next.js project: ${projectName}...`)
    );

    // Use spawnSync to allow interactive input
    const result = spawnSync("npx", ["create-next-app@latest", projectName], {
      stdio: "inherit",
      shell: true,
    });

    if (result.error) {
      throw new Error(
        `Failed to create Next.js project: ${result.error.message}`
      );
    } else if (result.status !== 0) {
      throw new Error(
        `Next.js project creation failed with exit code: ${result.status}`
      );
    } else {
      console.log(chalk.green("\n✅ Next.js project setup completed! 🚀"));
      console.log(chalk.cyan("\n📋 Next steps:"));
      console.log(chalk.white(`   1. cd ${projectName}`));
      console.log(chalk.white("   2. npm run dev"));
      console.log(chalk.white("   3. Open http://localhost:3000"));
    }
  } catch (error) {
    handleError(error, "Next.js setup");
  }
}

async function setupCustom() {
  try {
    const { projectType } = await inquirer.prompt([
      {
        type: "list",
        name: "projectType",
        message: "What type of project do you want to set up?",
        choices: [
          { name: "🎨 Frontend Only", value: "Frontend" },
          { name: "⚙️ Backend Only", value: "Backend" },
          { name: "🔗 Full Stack", value: "Full Stack" },
        ],
      },
    ]);

    if (projectType === "Frontend") {
      await setupCustomFrontend();
    } else if (projectType === "Backend") {
      await setupCustomBackend();
    } else {
      console.log(chalk.yellow("🚧 Custom full-stack setup is coming soon!"));
      console.log(
        chalk.cyan(
          "💡 For now, please use the MERN stack option for full-stack projects."
        )
      );
    }
  } catch (error) {
    handleError(error, "Custom setup");
  }
}

async function setupCustomFrontend() {
  const { framework } = await inquirer.prompt([
    {
      type: "list",
      name: "framework",
      message: "Choose a frontend framework:",
      choices: [
        { name: "🟢 Vue.js", value: "Vue.js" },
        { name: "🔶 Svelte", value: "Svelte" },
        { name: "🚀 Astro", value: "Astro" },
        { name: "🔴 Angular", value: "Angular" },
      ],
    },
  ]);

  const { projectName } = await inquirer.prompt([
    {
      type: "input",
      name: "projectName",
      message: `Enter your ${framework} project name:`,
      default: `my-${framework.toLowerCase()}-app`,
      validate: (input) => {
        const validation = validateProjectName(input);
        return validation.isValid || validation.error;
      },
    },
  ]);

  console.log(
    chalk.green(`\n🎨 Setting up ${framework} project: ${projectName}...`)
  );

  try {
    let success = false;

    switch (framework) {
      case "Vue.js":
        success = await executeCommand(
          `npm create vite@latest ${projectName} -- --template vue`,
          "Creating Vue.js project with Vite"
        );
        break;
      case "Svelte":
        success = await executeCommand(
          `npm create vite@latest ${projectName} -- --template svelte`,
          "Creating Svelte project with Vite"
        );
        break;
      case "Astro":
        success = await executeCommand(
          `npm create astro@latest ${projectName}`,
          "Creating Astro project"
        );
        break;
      case "Angular":
        // Ask for Angular-specific options
        const { angularOptions } = await inquirer.prompt([
          {
            type: "checkbox",
            name: "angularOptions",
            message: "Select Angular features:",
            choices: [
              { name: "Routing", value: "--routing", checked: true },
              { name: "SCSS Styling", value: "--style=scss" },
              { name: "Strict Mode", value: "--strict" },
              { name: "Skip Tests", value: "--skip-tests" },
            ],
          },
        ]);

        const angularFlags = angularOptions.join(" ");
        success = await executeCommand(
          `npx @angular/cli@latest new ${projectName} ${angularFlags}`,
          "Creating Angular project with selected options"
        );

        if (success) {
          // Add additional Angular enhancements
          const originalDir = process.cwd();
          shell.cd(projectName);

          try {
            // Install popular Angular packages
            const { installExtras } = await inquirer.prompt([
              {
                type: "confirm",
                name: "installExtras",
                message:
                  "Install popular Angular packages (Angular Material, HttpClient, Forms)?",
                default: true,
              },
            ]);

            if (installExtras) {
              await executeCommand(
                "ng add @angular/material --skip-confirmation",
                "Installing Angular Material"
              );

              await executeCommand(
                "npm install @angular/common@latest",
                "Installing Angular Common modules"
              );
            }
          } catch (error) {
            console.log(
              chalk.yellow(
                "⚠️ Some Angular enhancements failed, but project was created successfully"
              )
            );
          }

          shell.cd(originalDir);
        }
        break;
    }

    if (success) {
      console.log(chalk.green(`\n✅ ${framework} project setup completed! 🚀`));
      console.log(chalk.cyan("\n📋 Next steps:"));
      console.log(chalk.white(`   1. cd ${projectName}`));
      console.log(chalk.white("   2. npm install"));
      console.log(chalk.white("   3. npm run dev"));
    }
  } catch (error) {
    handleError(error, `${framework} setup`);
  }
}

async function setupCustomBackend() {
  const { backendFramework } = await inquirer.prompt([
    {
      type: "list",
      name: "backendFramework",
      message: "Choose a backend framework:",
      choices: [
        { name: "🟢 Express.js", value: "Express.js" },
        { name: "⚡ Fastify (Coming Soon)", value: "Fastify", disabled: true },
        { name: "🏗️ NestJS (Coming Soon)", value: "NestJS", disabled: true },
      ],
    },
  ]);

  const { projectName } = await inquirer.prompt([
    {
      type: "input",
      name: "projectName",
      message: `Enter your ${backendFramework} project name:`,
      default: `my-${backendFramework.toLowerCase()}-api`,
      validate: (input) => {
        const validation = validateProjectName(input);
        return validation.isValid || validation.error;
      },
    },
  ]);

  console.log(
    chalk.green(
      `\n⚙️ Setting up ${backendFramework} project: ${projectName}...`
    )
  );

  if (!createDirectory(projectName)) {
    throw new Error(`Failed to create project directory: ${projectName}`);
  }

  const originalDir = process.cwd();
  shell.cd(projectName);

  try {
    await setupBackend();
    console.log(
      chalk.green(`\n✅ ${backendFramework} backend setup completed! 🚀`)
    );
    console.log(chalk.cyan("\n📋 Next steps:"));
    console.log(chalk.white(`   1. cd ${projectName}`));
    console.log(chalk.white("   2. npm run dev"));
  } catch (error) {
    shell.cd(originalDir);
    throw error;
  }

  shell.cd(originalDir);
}

async function createWorkspaceConfig(projectName) {
  const workspacePackageJson = {
    name: projectName,
    version: "1.0.0",
    description: `${projectName} - MERN Stack Application`,
    private: true,
    workspaces: ["server", "client"],
    scripts: {
      dev: 'concurrently "npm run server" "npm run client"',
      server: "cd server && npm run dev",
      client: "cd client && npm run dev",
      "install-all":
        "npm install && npm run install-server && npm run install-client",
      "install-server": "cd server && npm install",
      "install-client": "cd client && npm install",
    },
    devDependencies: {
      concurrently: "^8.2.2",
    },
    author: "Generated by StackWizard",
    license: "MIT",
  };

  if (
    !writeFile("package.json", JSON.stringify(workspacePackageJson, null, 2))
  ) {
    throw new Error("Failed to create workspace package.json");
  }

  // Install concurrently for running both server and client
  const success = await executeCommand(
    "npm install",
    "Installing workspace dependencies"
  );

  if (!success) {
    throw new Error("Failed to install workspace dependencies");
  }
}

main();
