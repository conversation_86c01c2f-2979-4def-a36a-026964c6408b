#!/usr/bin/env node
import inquirer from "inquirer";
import chalk from "chalk";
import shell from "shelljs";
import figlet from "figlet";
import { spawnSync } from "child_process";
import ora from "ora";

import setupBackend from "./mern/backend/setupBackend.js";
import setupFrontend from "./mern/frontend/setupFrontend.js";
import {
  validateProjectName,
  checkPrerequisites,
  handleError,
  createProgressSpinner,
  createDirectory,
  executeCommand,
  writeFile,
} from "./utils/helpers.js";
import {
  getTemplateChoices,
  getTemplateByName,
  generateTemplateFiles,
} from "./templates/project-templates.js";
import { dockerTemplates, cicdTemplates } from "./config/templates.js";

console.log(chalk.green(figlet.textSync("StackWizard")));
console.log(
  chalk.cyan("🚀 Welcome to StackWizard - Your Project Setup Assistant\n")
);

async function main() {
  try {
    // Check prerequisites
    console.log(chalk.yellow("🔍 Checking prerequisites..."));
    const prereqCheck = checkPrerequisites();

    if (!prereqCheck.isValid) {
      console.log(chalk.red("❌ Missing required tools:"));
      prereqCheck.missing.forEach((tool) => {
        console.log(chalk.red(`   - ${tool}`));
      });
      console.log(
        chalk.yellow("\nPlease install the missing tools and try again.")
      );
      process.exit(1);
    }

    console.log(chalk.green("✅ All prerequisites satisfied!\n"));

    const answers = await inquirer.prompt([
      {
        type: "list",
        name: "stack",
        message: "Choose your stack:",
        choices: [
          {
            name: "🔥 MERN Stack (MongoDB, Express, React, Node.js)",
            value: "MERN",
          },
          {
            name: "📋 MERN with Template (E-commerce, Blog, Dashboard, etc.)",
            value: "MERN_TEMPLATE",
          },
          { name: "⚡ Next.js (React Framework)", value: "Next.js" },
          { name: "🛠️ Other Frameworks", value: "Other" },
        ],
      },
    ]);

    console.log(chalk.blue(`\n✨ You selected: ${answers.stack}\n`));

    switch (answers.stack) {
      case "MERN":
        await setupMERN();
        break;
      case "MERN_TEMPLATE":
        await setupMERNWithTemplate();
        break;
      case "Next.js":
        await setupNEXTJS();
        break;
      case "Other":
        await setupCustom();
        break;
      default:
        throw new Error("Invalid stack selection");
    }

    console.log(chalk.green("\n🎉 Project setup completed successfully!"));
    console.log(chalk.cyan("Happy coding! 🚀\n"));
  } catch (error) {
    handleError(error, "Main setup process");
  }
}

async function setupMERN() {
  try {
    const { projectName } = await inquirer.prompt([
      {
        type: "input",
        name: "projectName",
        message: "Enter your MERN project name:",
        default: "mern-project",
        validate: (input) => {
          const validation = validateProjectName(input);
          return validation.isValid || validation.error;
        },
      },
    ]);

    console.log(chalk.green(`\n📁 Creating project: ${projectName}\n`));

    // Step 1: Create project directory
    if (!createDirectory(projectName)) {
      throw new Error(`Failed to create project directory: ${projectName}`);
    }

    const originalDir = process.cwd();
    shell.cd(projectName);

    try {
      // Step 2: Setup Backend
      console.log(chalk.yellow("🔧 Setting up backend..."));
      await setupBackend();

      // Step 3: Setup Frontend
      console.log(chalk.yellow("🎨 Setting up frontend..."));
      await setupFrontend();

      // Step 4: Create root package.json for workspace
      console.log(chalk.yellow("📦 Creating workspace configuration..."));
      await createWorkspaceConfig(projectName);

      console.log(
        chalk.green(`\n✅ ${projectName} MERN stack setup completed! 🚀`)
      );
      console.log(chalk.cyan("\n📋 Next steps:"));
      console.log(chalk.white(`   1. cd ${projectName}`));
      console.log(chalk.white("   2. Start backend: cd server && npm run dev"));
      console.log(
        chalk.white("   3. Start frontend: cd client && npm run dev")
      );
    } catch (error) {
      shell.cd(originalDir);
      throw error;
    }

    shell.cd(originalDir);
  } catch (error) {
    handleError(error, "MERN stack setup");
  }
}

async function setupMERNWithTemplate() {
  try {
    // First, let user choose a template
    const { template } = await inquirer.prompt([
      {
        type: "list",
        name: "template",
        message: "Choose a project template:",
        choices: getTemplateChoices(),
        pageSize: 10,
      },
    ]);

    const selectedTemplate = getTemplateByName(template);
    console.log(chalk.blue(`\n✨ Selected: ${selectedTemplate.name}`));
    console.log(chalk.gray(`${selectedTemplate.description}\n`));

    // Show template features
    console.log(chalk.cyan("🚀 This template includes:"));
    selectedTemplate.features.forEach((feature) => {
      console.log(chalk.white(`   • ${feature}`));
    });
    console.log("");

    const { projectName } = await inquirer.prompt([
      {
        type: "input",
        name: "projectName",
        message: "Enter your project name:",
        default: `my-${template}-app`,
        validate: (input) => {
          const validation = validateProjectName(input);
          return validation.isValid || validation.error;
        },
      },
    ]);

    console.log(
      chalk.green(`\n📁 Creating ${selectedTemplate.name}: ${projectName}\n`)
    );

    // Step 1: Create project directory
    if (!createDirectory(projectName)) {
      throw new Error(`Failed to create project directory: ${projectName}`);
    }

    const originalDir = process.cwd();
    shell.cd(projectName);

    try {
      // Step 2: Setup Backend with template-specific enhancements
      console.log(chalk.yellow("🔧 Setting up enhanced backend..."));
      await setupBackend();

      // Step 3: Setup Frontend with template-specific styling
      console.log(chalk.yellow("🎨 Setting up enhanced frontend..."));
      await setupFrontend();

      // Step 4: Generate template-specific files
      console.log(chalk.yellow("📋 Generating template-specific files..."));
      const templateFiles = generateTemplateFiles(template, projectName);

      for (const [filePath, content] of Object.entries(templateFiles)) {
        const dir = filePath.split("/").slice(0, -1).join("/");
        if (dir && !createDirectory(dir)) {
          console.log(chalk.yellow(`⚠️ Failed to create directory: ${dir}`));
          continue;
        }

        if (!writeFile(filePath, content)) {
          console.log(chalk.yellow(`⚠️ Failed to create file: ${filePath}`));
        }
      }

      // Step 5: Install template-specific packages
      if (selectedTemplate.backend.requiredPackages?.length > 0) {
        console.log(
          chalk.yellow("📦 Installing template-specific backend packages...")
        );
        shell.cd("server");
        const backendSuccess = await executeCommand(
          `npm install ${selectedTemplate.backend.requiredPackages.join(" ")}`,
          `Installing ${selectedTemplate.backend.requiredPackages.join(", ")}`
        );
        if (!backendSuccess) {
          console.log(
            chalk.yellow("⚠️ Some backend packages failed to install")
          );
        }
        shell.cd("..");
      }

      if (selectedTemplate.frontend.additionalPackages?.length > 0) {
        console.log(
          chalk.yellow("📦 Installing template-specific frontend packages...")
        );
        shell.cd("client");
        const frontendSuccess = await executeCommand(
          `npm install ${selectedTemplate.frontend.additionalPackages.join(
            " "
          )}`,
          `Installing ${selectedTemplate.frontend.additionalPackages.join(
            ", "
          )}`
        );
        if (!frontendSuccess) {
          console.log(
            chalk.yellow("⚠️ Some frontend packages failed to install")
          );
        }
        shell.cd("..");
      }

      // Step 6: Create workspace configuration
      console.log(chalk.yellow("📦 Creating workspace configuration..."));
      await createWorkspaceConfig(projectName);

      // Step 7: Ask for additional features
      const { additionalFeatures } = await inquirer.prompt([
        {
          type: "checkbox",
          name: "additionalFeatures",
          message: "Select additional features to include:",
          choices: [
            { name: "🐳 Docker configuration", value: "docker" },
            { name: "🔄 GitHub Actions CI/CD", value: "github-actions" },
            { name: "🦊 GitLab CI/CD", value: "gitlab-ci" },
            { name: "📊 ESLint + Prettier configuration", value: "linting" },
            { name: "🧪 Jest testing setup", value: "testing" },
          ],
        },
      ]);

      // Generate additional feature files
      if (additionalFeatures.length > 0) {
        console.log(chalk.yellow("⚙️ Setting up additional features..."));
        await setupAdditionalFeatures(additionalFeatures, projectName);
      }

      console.log(
        chalk.green(`\n✅ ${selectedTemplate.name} setup completed! 🚀`)
      );
      console.log(chalk.cyan("\n📋 Template Features Included:"));
      selectedTemplate.features.forEach((feature) => {
        console.log(chalk.white(`   • ${feature}`));
      });

      console.log(chalk.cyan("\n📋 Next steps:"));
      console.log(chalk.white(`   1. cd ${projectName}`));
      console.log(chalk.white("   2. Start backend: cd server && npm run dev"));
      console.log(
        chalk.white("   3. Start frontend: cd client && npm run dev")
      );
      console.log(
        chalk.white("   4. Check README.md for template-specific instructions")
      );
    } catch (error) {
      shell.cd(originalDir);
      throw error;
    }

    shell.cd(originalDir);
  } catch (error) {
    handleError(error, "MERN template setup");
  }
}

async function setupNEXTJS() {
  try {
    const { projectName } = await inquirer.prompt([
      {
        type: "input",
        name: "projectName",
        message: "Enter your Next.js project name:",
        default: "nextjs-app",
        validate: (input) => {
          const validation = validateProjectName(input);
          return validation.isValid || validation.error;
        },
      },
    ]);

    console.log(
      chalk.green(`\n⚡ Creating Next.js project: ${projectName}...`)
    );

    // Use spawnSync to allow interactive input
    const result = spawnSync("npx", ["create-next-app@latest", projectName], {
      stdio: "inherit",
      shell: true,
    });

    if (result.error) {
      throw new Error(
        `Failed to create Next.js project: ${result.error.message}`
      );
    } else if (result.status !== 0) {
      throw new Error(
        `Next.js project creation failed with exit code: ${result.status}`
      );
    } else {
      console.log(chalk.green("\n✅ Next.js project setup completed! 🚀"));
      console.log(chalk.cyan("\n📋 Next steps:"));
      console.log(chalk.white(`   1. cd ${projectName}`));
      console.log(chalk.white("   2. npm run dev"));
      console.log(chalk.white("   3. Open http://localhost:3000"));
    }
  } catch (error) {
    handleError(error, "Next.js setup");
  }
}

async function setupCustom() {
  try {
    const { projectType } = await inquirer.prompt([
      {
        type: "list",
        name: "projectType",
        message: "What type of project do you want to set up?",
        choices: [
          { name: "🎨 Frontend Only", value: "Frontend" },
          { name: "⚙️ Backend Only", value: "Backend" },
          { name: "🔗 Full Stack", value: "Full Stack" },
        ],
      },
    ]);

    if (projectType === "Frontend") {
      await setupCustomFrontend();
    } else if (projectType === "Backend") {
      await setupCustomBackend();
    } else {
      console.log(chalk.yellow("🚧 Custom full-stack setup is coming soon!"));
      console.log(
        chalk.cyan(
          "💡 For now, please use the MERN stack option for full-stack projects."
        )
      );
    }
  } catch (error) {
    handleError(error, "Custom setup");
  }
}

async function setupCustomFrontend() {
  const { framework } = await inquirer.prompt([
    {
      type: "list",
      name: "framework",
      message: "Choose a frontend framework:",
      choices: [
        { name: "🟢 Vue.js", value: "Vue.js" },
        { name: "🔶 Svelte", value: "Svelte" },
        { name: "🚀 Astro", value: "Astro" },
        { name: "🔴 Angular", value: "Angular" },
      ],
    },
  ]);

  const { projectName } = await inquirer.prompt([
    {
      type: "input",
      name: "projectName",
      message: `Enter your ${framework} project name:`,
      default: `my-${framework.toLowerCase()}-app`,
      validate: (input) => {
        const validation = validateProjectName(input);
        return validation.isValid || validation.error;
      },
    },
  ]);

  console.log(
    chalk.green(`\n🎨 Setting up ${framework} project: ${projectName}...`)
  );

  try {
    let success = false;

    switch (framework) {
      case "Vue.js":
        success = await executeCommand(
          `npm create vite@latest ${projectName} -- --template vue`,
          "Creating Vue.js project with Vite"
        );
        break;
      case "Svelte":
        success = await executeCommand(
          `npm create vite@latest ${projectName} -- --template svelte`,
          "Creating Svelte project with Vite"
        );
        break;
      case "Astro":
        success = await executeCommand(
          `npm create astro@latest ${projectName}`,
          "Creating Astro project"
        );
        break;
      case "Angular":
        // Ask for Angular-specific options
        const { angularOptions } = await inquirer.prompt([
          {
            type: "checkbox",
            name: "angularOptions",
            message: "Select Angular features:",
            choices: [
              { name: "Routing", value: "--routing", checked: true },
              { name: "SCSS Styling", value: "--style=scss" },
              { name: "Strict Mode", value: "--strict" },
              { name: "Skip Tests", value: "--skip-tests" },
            ],
          },
        ]);

        const angularFlags = angularOptions.join(" ");
        success = await executeCommand(
          `npx @angular/cli@latest new ${projectName} ${angularFlags}`,
          "Creating Angular project with selected options"
        );

        if (success) {
          // Add additional Angular enhancements
          const originalDir = process.cwd();
          shell.cd(projectName);

          try {
            // Install popular Angular packages
            const { installExtras } = await inquirer.prompt([
              {
                type: "confirm",
                name: "installExtras",
                message:
                  "Install popular Angular packages (Angular Material, HttpClient, Forms)?",
                default: true,
              },
            ]);

            if (installExtras) {
              await executeCommand(
                "ng add @angular/material --skip-confirmation",
                "Installing Angular Material"
              );

              await executeCommand(
                "npm install @angular/common@latest",
                "Installing Angular Common modules"
              );
            }
          } catch (error) {
            console.log(
              chalk.yellow(
                "⚠️ Some Angular enhancements failed, but project was created successfully"
              )
            );
          }

          shell.cd(originalDir);
        }
        break;
    }

    if (success) {
      console.log(chalk.green(`\n✅ ${framework} project setup completed! 🚀`));
      console.log(chalk.cyan("\n📋 Next steps:"));
      console.log(chalk.white(`   1. cd ${projectName}`));
      console.log(chalk.white("   2. npm install"));
      console.log(chalk.white("   3. npm run dev"));
    }
  } catch (error) {
    handleError(error, `${framework} setup`);
  }
}

async function setupCustomBackend() {
  const { backendFramework } = await inquirer.prompt([
    {
      type: "list",
      name: "backendFramework",
      message: "Choose a backend framework:",
      choices: [
        { name: "🟢 Express.js", value: "Express.js" },
        { name: "⚡ Fastify (Coming Soon)", value: "Fastify", disabled: true },
        { name: "🏗️ NestJS (Coming Soon)", value: "NestJS", disabled: true },
      ],
    },
  ]);

  const { projectName } = await inquirer.prompt([
    {
      type: "input",
      name: "projectName",
      message: `Enter your ${backendFramework} project name:`,
      default: `my-${backendFramework.toLowerCase()}-api`,
      validate: (input) => {
        const validation = validateProjectName(input);
        return validation.isValid || validation.error;
      },
    },
  ]);

  console.log(
    chalk.green(
      `\n⚙️ Setting up ${backendFramework} project: ${projectName}...`
    )
  );

  if (!createDirectory(projectName)) {
    throw new Error(`Failed to create project directory: ${projectName}`);
  }

  const originalDir = process.cwd();
  shell.cd(projectName);

  try {
    await setupBackend();
    console.log(
      chalk.green(`\n✅ ${backendFramework} backend setup completed! 🚀`)
    );
    console.log(chalk.cyan("\n📋 Next steps:"));
    console.log(chalk.white(`   1. cd ${projectName}`));
    console.log(chalk.white("   2. npm run dev"));
  } catch (error) {
    shell.cd(originalDir);
    throw error;
  }

  shell.cd(originalDir);
}

async function createWorkspaceConfig(projectName) {
  const workspacePackageJson = {
    name: projectName,
    version: "1.0.0",
    description: `${projectName} - MERN Stack Application`,
    private: true,
    workspaces: ["server", "client"],
    scripts: {
      dev: 'concurrently "npm run server" "npm run client"',
      server: "cd server && npm run dev",
      client: "cd client && npm run dev",
      "install-all":
        "npm install && npm run install-server && npm run install-client",
      "install-server": "cd server && npm install",
      "install-client": "cd client && npm install",
    },
    devDependencies: {
      concurrently: "^8.2.2",
    },
    author: "Generated by StackWizard",
    license: "MIT",
  };

  if (
    !writeFile("package.json", JSON.stringify(workspacePackageJson, null, 2))
  ) {
    throw new Error("Failed to create workspace package.json");
  }

  // Install concurrently for running both server and client
  const success = await executeCommand(
    "npm install",
    "Installing workspace dependencies"
  );

  if (!success) {
    throw new Error("Failed to install workspace dependencies");
  }
}

async function setupAdditionalFeatures(features, projectName) {
  try {
    for (const feature of features) {
      switch (feature) {
        case "docker":
          console.log(chalk.blue("🐳 Setting up Docker configuration..."));

          // Create Dockerfile for backend
          if (!writeFile("server/Dockerfile", dockerTemplates.backend)) {
            console.log(chalk.yellow("⚠️ Failed to create backend Dockerfile"));
          }

          // Create Dockerfile for frontend
          if (!writeFile("client/Dockerfile", dockerTemplates.frontend)) {
            console.log(
              chalk.yellow("⚠️ Failed to create frontend Dockerfile")
            );
          }

          // Create docker-compose.yml
          if (!writeFile("docker-compose.yml", dockerTemplates.compose)) {
            console.log(chalk.yellow("⚠️ Failed to create docker-compose.yml"));
          }

          // Create .dockerignore files
          const dockerignore = `node_modules
npm-debug.log
.git
.gitignore
README.md
.env
.nyc_output
coverage
.coverage
.coverage.*
.cache`;

          writeFile("server/.dockerignore", dockerignore);
          writeFile("client/.dockerignore", dockerignore);

          console.log(chalk.green("✅ Docker configuration created"));
          break;

        case "github-actions":
          console.log(chalk.blue("🔄 Setting up GitHub Actions..."));

          if (!createDirectory(".github/workflows")) {
            console.log(
              chalk.yellow("⚠️ Failed to create .github/workflows directory")
            );
            break;
          }

          if (!writeFile(".github/workflows/ci.yml", cicdTemplates.github)) {
            console.log(
              chalk.yellow("⚠️ Failed to create GitHub Actions workflow")
            );
          } else {
            console.log(chalk.green("✅ GitHub Actions workflow created"));
          }
          break;

        case "gitlab-ci":
          console.log(chalk.blue("🦊 Setting up GitLab CI..."));

          if (!writeFile(".gitlab-ci.yml", cicdTemplates.gitlab)) {
            console.log(
              chalk.yellow("⚠️ Failed to create GitLab CI configuration")
            );
          } else {
            console.log(chalk.green("✅ GitLab CI configuration created"));
          }
          break;

        case "linting":
          console.log(chalk.blue("📊 Setting up ESLint and Prettier..."));

          // Install ESLint and Prettier in both server and client
          shell.cd("server");
          await executeCommand(
            "npm install --save-dev eslint prettier eslint-config-prettier eslint-plugin-prettier",
            "Installing ESLint and Prettier for backend"
          );

          // Create ESLint config for backend
          const backendEslintConfig = {
            env: {
              node: true,
              es2021: true,
            },
            extends: ["eslint:recommended", "prettier"],
            plugins: ["prettier"],
            rules: {
              "prettier/prettier": "error",
              "no-unused-vars": ["error", { argsIgnorePattern: "^_" }],
              "no-console": "off",
            },
          };

          writeFile(
            ".eslintrc.json",
            JSON.stringify(backendEslintConfig, null, 2)
          );

          shell.cd("../client");
          await executeCommand(
            "npm install --save-dev eslint prettier eslint-config-prettier eslint-plugin-prettier eslint-plugin-react eslint-plugin-react-hooks",
            "Installing ESLint and Prettier for frontend"
          );

          // Create ESLint config for frontend
          const frontendEslintConfig = {
            env: {
              browser: true,
              es2021: true,
            },
            extends: [
              "eslint:recommended",
              "plugin:react/recommended",
              "plugin:react-hooks/recommended",
              "prettier",
            ],
            plugins: ["react", "react-hooks", "prettier"],
            rules: {
              "prettier/prettier": "error",
              "react/react-in-jsx-scope": "off",
              "react/prop-types": "off",
            },
            settings: {
              react: {
                version: "detect",
              },
            },
          };

          writeFile(
            ".eslintrc.json",
            JSON.stringify(frontendEslintConfig, null, 2)
          );

          // Create Prettier config
          const prettierConfig = {
            semi: true,
            trailingComma: "es5",
            singleQuote: true,
            printWidth: 80,
            tabWidth: 2,
          };

          writeFile(".prettierrc", JSON.stringify(prettierConfig, null, 2));
          shell.cd("../server");
          writeFile(".prettierrc", JSON.stringify(prettierConfig, null, 2));

          shell.cd("..");
          console.log(chalk.green("✅ ESLint and Prettier configured"));
          break;

        case "testing":
          console.log(chalk.blue("🧪 Setting up Jest testing..."));

          // Setup testing for backend
          shell.cd("server");
          await executeCommand(
            "npm install --save-dev jest supertest",
            "Installing Jest for backend testing"
          );

          // Create basic test file
          const backendTest = `const request = require('supertest');
const app = require('../server');

describe('API Tests', () => {
  test('GET / should return API status', async () => {
    const response = await request(app).get('/');
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
  });
});`;

          if (!createDirectory("tests")) {
            console.log(chalk.yellow("⚠️ Failed to create tests directory"));
          } else {
            writeFile("tests/api.test.js", backendTest);
          }

          // Setup testing for frontend
          shell.cd("../client");
          await executeCommand(
            "npm install --save-dev @testing-library/react @testing-library/jest-dom @testing-library/user-event vitest jsdom",
            "Installing testing libraries for frontend"
          );

          // Create basic test file
          const frontendTest = `import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import App from '../src/App';

describe('App Component', () => {
  it('renders welcome message', () => {
    render(<App />);
    const welcomeElement = screen.getByText(/welcome/i);
    expect(welcomeElement).toBeInTheDocument();
  });
});`;

          if (!createDirectory("src/__tests__")) {
            console.log(chalk.yellow("⚠️ Failed to create tests directory"));
          } else {
            writeFile("src/__tests__/App.test.jsx", frontendTest);
          }

          shell.cd("..");
          console.log(chalk.green("✅ Testing setup completed"));
          break;

        default:
          console.log(chalk.yellow(`⚠️ Unknown feature: ${feature}`));
      }
    }
  } catch (error) {
    console.log(
      chalk.red(`❌ Error setting up additional features: ${error.message}`)
    );
  }
}

main();
