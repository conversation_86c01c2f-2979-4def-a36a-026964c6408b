import { vi } from 'vitest';

// Global test setup
beforeEach(() => {
  // Reset all mocks before each test
  vi.clearAllMocks();
  
  // Reset console methods
  vi.spyOn(console, 'log').mockImplementation(() => {});
  vi.spyOn(console, 'error').mockImplementation(() => {});
  vi.spyOn(console, 'warn').mockImplementation(() => {});
});

afterEach(() => {
  // Restore all mocks after each test
  vi.restoreAllMocks();
});

// Mock process.exit to prevent tests from actually exiting
vi.spyOn(process, 'exit').mockImplementation(() => {});

// Mock process.cwd
vi.spyOn(process, 'cwd').mockReturnValue('/test/directory');
