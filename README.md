# my-ecommerce-app

Complete e-commerce solution with product management, cart, and payments

## 🚀 Features

- ✅ Product catalog with categories
- ✅ Shopping cart functionality
- ✅ User authentication & profiles
- ✅ Order management system
- ✅ Payment integration ready
- ✅ Admin dashboard
- ✅ Responsive design

## 🛠️ Technology Stack

### Backend
- Node.js & Express.js
- MongoDB with Mongoose
- JWT Authentication
- Additional packages: stripe, multer, sharp

### Frontend
- React.js with Vite
- Tailwind CSS
- Additional packages: @stripe/stripe-js, react-router-dom, axios

## 📋 Quick Start

1. Install dependencies:
```bash
npm run install-all
```

2. Set up environment variables:
```bash
cp server/.env.example server/.env
# Edit server/.env with your configuration
```

3. Start development servers:
```bash
npm run dev
```

## 📁 Project Structure

This project follows the MERN stack architecture with additional components for e-commerce platform.

Built with ❤️ using StackWizard
