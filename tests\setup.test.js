import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import fs from 'fs';
import shell from 'shelljs';
import setupBackend from '../mern/backend/setupBackend.js';
import setupFrontend from '../mern/frontend/setupFrontend.js';

// Mock dependencies
vi.mock('fs');
vi.mock('shelljs');
vi.mock('inquirer');
vi.mock('../utils/helpers.js', () => ({
  executeCommand: vi.fn(),
  handleError: vi.fn(),
  writeFile: vi.fn(),
  createDirectory: vi.fn()
}));

import { executeCommand, writeFile, createDirectory } from '../utils/helpers.js';

describe('Setup Functions', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Default successful mocks
    executeCommand.mockResolvedValue(true);
    writeFile.mockReturnValue(true);
    createDirectory.mockReturnValue(true);
    shell.cd.mockReturnValue(undefined);
    fs.readFileSync.mockReturnValue('{"name": "test"}');
  });

  describe('setupBackend', () => {
    it('should create backend structure successfully', async () => {
      // Mock inquirer responses
      const inquirer = await import('inquirer');
      inquirer.prompt = vi.fn()
        .mockResolvedValueOnce({ install: false }) // JWT
        .mockResolvedValueOnce({ install: false }) // bcrypt
        .mockResolvedValueOnce({ install: false }) // multer
        .mockResolvedValueOnce({ install: false }) // cookie-parser
        .mockResolvedValueOnce({ install: false }) // compression
        .mockResolvedValueOnce({ install: false }) // helmet
        .mockResolvedValueOnce({ install: false }); // rate-limit

      await setupBackend();

      // Verify directory creation
      expect(createDirectory).toHaveBeenCalledWith('server');
      
      // Verify package.json initialization
      expect(executeCommand).toHaveBeenCalledWith(
        'npm init -y',
        'Initializing package.json'
      );

      // Verify mandatory dependencies installation
      expect(executeCommand).toHaveBeenCalledWith(
        'npm install express mongoose dotenv cors',
        expect.stringContaining('Installing mandatory dependencies')
      );

      // Verify dev dependencies installation
      expect(executeCommand).toHaveBeenCalledWith(
        'npm install --save-dev nodemon',
        'Installing dev dependencies: nodemon'
      );

      // Verify file creation
      expect(writeFile).toHaveBeenCalledWith('package.json', expect.any(String));
      expect(writeFile).toHaveBeenCalledWith('config/db.js', expect.any(String));
      expect(writeFile).toHaveBeenCalledWith('models/userModel.js', expect.any(String));
      expect(writeFile).toHaveBeenCalledWith('controllers/userController.js', expect.any(String));
      expect(writeFile).toHaveBeenCalledWith('routes/userRoutes.js', expect.any(String));
      expect(writeFile).toHaveBeenCalledWith('server.js', expect.any(String));
      expect(writeFile).toHaveBeenCalledWith('.env', expect.any(String));
    });

    it('should install optional dependencies when selected', async () => {
      const inquirer = await import('inquirer');
      inquirer.prompt = vi.fn()
        .mockResolvedValueOnce({ install: true }) // JWT
        .mockResolvedValueOnce({ install: true }) // bcrypt
        .mockResolvedValueOnce({ install: false }) // multer
        .mockResolvedValueOnce({ install: false }) // cookie-parser
        .mockResolvedValueOnce({ install: false }) // compression
        .mockResolvedValueOnce({ install: false }) // helmet
        .mockResolvedValueOnce({ install: false }); // rate-limit

      await setupBackend();

      // Verify optional dependencies installation
      expect(executeCommand).toHaveBeenCalledWith(
        'npm install jsonwebtoken bcryptjs',
        expect.stringContaining('Installing optional dependencies')
      );

      // Verify auth middleware creation
      expect(writeFile).toHaveBeenCalledWith('middleware/authMiddleware.js', expect.any(String));
    });

    it('should handle installation failures gracefully', async () => {
      executeCommand.mockResolvedValueOnce(false); // npm init fails

      const inquirer = await import('inquirer');
      inquirer.prompt = vi.fn().mockResolvedValue({ install: false });

      await expect(setupBackend()).rejects.toThrow('Failed to initialize package.json');
    });

    it('should handle file creation failures', async () => {
      writeFile.mockReturnValueOnce(false); // package.json write fails

      const inquirer = await import('inquirer');
      inquirer.prompt = vi.fn().mockResolvedValue({ install: false });

      await expect(setupBackend()).rejects.toThrow('Failed to update package.json');
    });
  });

  describe('setupFrontend', () => {
    it('should create React frontend successfully', async () => {
      const inquirer = await import('inquirer');
      inquirer.prompt = vi.fn()
        .mockResolvedValueOnce({ framework: 'React.js' })
        .mockResolvedValueOnce({ styling: 'None' });

      await setupFrontend();

      // Verify directory creation
      expect(createDirectory).toHaveBeenCalledWith('client');

      // Verify Vite project creation
      expect(executeCommand).toHaveBeenCalledWith(
        'npm create vite@latest . -- --template react',
        'Creating Vite project with React.js'
      );

      // Verify dependencies installation
      expect(executeCommand).toHaveBeenCalledWith(
        'npm install',
        'Installing frontend dependencies'
      );

      // Verify App component creation
      expect(writeFile).toHaveBeenCalledWith(
        'src/App.jsx',
        expect.stringContaining('Welcome to Your Project!')
      );
    });

    it('should create TypeScript React frontend', async () => {
      const inquirer = await import('inquirer');
      inquirer.prompt = vi.fn()
        .mockResolvedValueOnce({ framework: 'React + TypeScript' })
        .mockResolvedValueOnce({ styling: 'None' });

      await setupFrontend();

      // Verify TypeScript template
      expect(executeCommand).toHaveBeenCalledWith(
        'npm create vite@latest . -- --template react-ts',
        'Creating Vite project with React + TypeScript'
      );

      // Verify TypeScript App component
      expect(writeFile).toHaveBeenCalledWith(
        'src/App.tsx',
        expect.stringContaining('import React from "react"')
      );
    });

    it('should install Tailwind CSS when selected', async () => {
      const inquirer = await import('inquirer');
      inquirer.prompt = vi.fn()
        .mockResolvedValueOnce({ framework: 'React.js' })
        .mockResolvedValueOnce({ styling: 'Tailwind CSS' });

      await setupFrontend();

      // Verify Tailwind installation
      expect(executeCommand).toHaveBeenCalledWith(
        'npm install -D tailwindcss postcss autoprefixer && npx tailwindcss init -p',
        'Installing and configuring Tailwind CSS'
      );

      // Verify Tailwind config creation
      expect(writeFile).toHaveBeenCalledWith(
        'tailwind.config.js',
        expect.stringContaining('tailwindcss')
      );
    });

    it('should install Bootstrap when selected', async () => {
      const inquirer = await import('inquirer');
      inquirer.prompt = vi.fn()
        .mockResolvedValueOnce({ framework: 'React.js' })
        .mockResolvedValueOnce({ styling: 'Bootstrap' });

      await setupFrontend();

      // Verify Bootstrap installation
      expect(executeCommand).toHaveBeenCalledWith(
        'npm install bootstrap@5.3.3',
        'Installing Bootstrap'
      );
    });

    it('should handle Vite creation failure', async () => {
      executeCommand.mockResolvedValueOnce(false); // Vite creation fails

      const inquirer = await import('inquirer');
      inquirer.prompt = vi.fn()
        .mockResolvedValueOnce({ framework: 'React.js' })
        .mockResolvedValueOnce({ styling: 'None' });

      await expect(setupFrontend()).rejects.toThrow('Failed to create Vite project');
    });

    it('should handle dependency installation failure', async () => {
      executeCommand
        .mockResolvedValueOnce(true) // Vite creation succeeds
        .mockResolvedValueOnce(false); // npm install fails

      const inquirer = await import('inquirer');
      inquirer.prompt = vi.fn()
        .mockResolvedValueOnce({ framework: 'React.js' })
        .mockResolvedValueOnce({ styling: 'None' });

      await expect(setupFrontend()).rejects.toThrow('Failed to install frontend dependencies');
    });
  });
});
