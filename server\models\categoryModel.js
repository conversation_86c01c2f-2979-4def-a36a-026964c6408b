import mongoose from "mongoose";

const categorySchema = new mongoose.Schema(
  {
    // Add your Category fields here
    name: {
      type: String,
      required: [true, "Category name is required"],
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

const Category = mongoose.model("Category", categorySchema);
export default Category;