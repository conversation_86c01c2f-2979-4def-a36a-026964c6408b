import mongoose from "mongoose";

const orderSchema = new mongoose.Schema(
  {
    // Add your Order fields here
    name: {
      type: String,
      required: [true, "Order name is required"],
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

const Order = mongoose.model("Order", orderSchema);
export default Order;