{"name": "stackwizard", "version": "1.0.1", "description": "A simple CLI tool to automate MERN and frontend stack setup", "main": "index.js", "type": "module", "scripts": {"dev": "node index.js"}, "keywords": ["cli", "mern", "stack", "project-setup", "react", "express", "vite", "automation"], "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"chalk": "^5.4.1", "figlet": "^1.8.0", "inquirer": "^12.5.0", "shelljs": "^0.9.2"}, "bin": {"stackwizard": "index.js"}}