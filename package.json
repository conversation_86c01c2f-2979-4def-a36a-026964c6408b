{"name": "stackwizard", "version": "1.0.1", "description": "A simple CLI tool to automate MERN and frontend stack setup", "main": "index.js", "type": "module", "scripts": {"dev": "node index.js", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["cli", "mern", "stack", "project-setup", "react", "express", "vite", "automation"], "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"chalk": "^5.4.1", "figlet": "^1.8.0", "inquirer": "^12.5.0", "ora": "^8.1.1", "shelljs": "^0.9.2"}, "devDependencies": {"@vitest/coverage-v8": "^2.1.8", "eslint": "^9.17.0", "vitest": "^2.1.8"}, "bin": {"stackwizard": "index.js"}}