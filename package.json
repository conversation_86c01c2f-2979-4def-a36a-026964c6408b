{"name": "stackwizard", "version": "1.0.1", "description": "A simple CLI tool to automate MERN and frontend stack setup", "main": "index.js", "type": "module", "scripts": {"dev": "node index.js", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["cli", "mern", "stack", "project-setup", "react", "express", "vite", "automation"], "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"@stripe/stripe-js": "^7.3.1", "axios": "^1.10.0", "chalk": "^5.4.1", "figlet": "^1.8.0", "inquirer": "^12.5.0", "multer": "^2.0.1", "ora": "^8.1.1", "react-router-dom": "^7.6.2", "sharp": "^0.34.2", "shelljs": "^0.9.2", "stripe": "^18.2.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@vitest/coverage-v8": "^2.1.8", "eslint": "^9.17.0", "prettier": "^3.4.2", "vitest": "^2.1.8"}, "bin": {"stackwizard": "index.js"}}