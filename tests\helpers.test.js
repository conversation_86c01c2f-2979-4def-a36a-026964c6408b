import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import fs from 'fs';
import shell from 'shelljs';
import {
  validateProjectName,
  checkPrerequisites,
  executeCommand,
  createDirectory,
  writeFile,
  checkPackageExists,
  getLatestVersion
} from '../utils/helpers.js';

// Mock dependencies
vi.mock('fs');
vi.mock('shelljs');
vi.mock('ora', () => ({
  default: vi.fn(() => ({
    start: vi.fn(),
    succeed: vi.fn(),
    fail: vi.fn()
  }))
}));

describe('Helper Functions', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('validateProjectName', () => {
    it('should validate correct project names', () => {
      const validNames = ['my-project', 'test123', 'valid_name', 'a'];
      
      validNames.forEach(name => {
        fs.existsSync.mockReturnValue(false);
        const result = validateProjectName(name);
        expect(result.isValid).toBe(true);
        expect(result.error).toBeNull();
      });
    });

    it('should reject invalid project names', () => {
      const invalidCases = [
        { name: '', expectedError: 'Project name cannot be empty' },
        { name: '   ', expectedError: 'Project name cannot be empty' },
        { name: 'My Project', expectedError: 'Project name must contain only lowercase letters' },
        { name: 'project!', expectedError: 'Project name must contain only lowercase letters' },
        { name: 'UPPERCASE', expectedError: 'Project name must contain only lowercase letters' },
        { name: 'a'.repeat(215), expectedError: 'Project name must be less than 214 characters' },
        { name: 'node_modules', expectedError: '"node_modules" is a reserved name' }
      ];

      invalidCases.forEach(({ name, expectedError }) => {
        fs.existsSync.mockReturnValue(false);
        const result = validateProjectName(name);
        expect(result.isValid).toBe(false);
        expect(result.error).toContain(expectedError.split(' ')[0]);
      });
    });

    it('should reject existing directory names', () => {
      fs.existsSync.mockReturnValue(true);
      const result = validateProjectName('existing-project');
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('already exists');
    });
  });

  describe('checkPrerequisites', () => {
    it('should return valid when all tools are available', () => {
      shell.which.mockImplementation((tool) => tool === 'node' || tool === 'npm');
      
      const result = checkPrerequisites();
      expect(result.isValid).toBe(true);
      expect(result.missing).toEqual([]);
    });

    it('should return missing tools when not available', () => {
      shell.which.mockReturnValue(false);
      
      const result = checkPrerequisites();
      expect(result.isValid).toBe(false);
      expect(result.missing).toEqual(['node', 'npm']);
    });
  });

  describe('executeCommand', () => {
    it('should return true for successful commands', async () => {
      shell.exec.mockReturnValue({ code: 0, stderr: '' });
      
      const result = await executeCommand('npm --version', 'Testing npm');
      expect(result).toBe(true);
      expect(shell.exec).toHaveBeenCalledWith('npm --version', { silent: true });
    });

    it('should return false for failed commands', async () => {
      shell.exec.mockReturnValue({ code: 1, stderr: 'Command failed' });
      
      const result = await executeCommand('invalid-command', 'Testing invalid command');
      expect(result).toBe(false);
    });

    it('should handle exceptions', async () => {
      shell.exec.mockImplementation(() => {
        throw new Error('Shell error');
      });
      
      const result = await executeCommand('error-command', 'Testing error');
      expect(result).toBe(false);
    });
  });

  describe('createDirectory', () => {
    it('should create directory successfully', () => {
      fs.existsSync.mockReturnValue(false);
      fs.mkdirSync.mockReturnValue(undefined);
      
      const result = createDirectory('test-dir');
      expect(result).toBe(true);
      expect(fs.mkdirSync).toHaveBeenCalledWith('test-dir', { recursive: true });
    });

    it('should return true if directory already exists', () => {
      fs.existsSync.mockReturnValue(true);
      
      const result = createDirectory('existing-dir');
      expect(result).toBe(true);
      expect(fs.mkdirSync).not.toHaveBeenCalled();
    });

    it('should handle creation errors', () => {
      fs.existsSync.mockReturnValue(false);
      fs.mkdirSync.mockImplementation(() => {
        throw new Error('Permission denied');
      });
      
      const result = createDirectory('error-dir');
      expect(result).toBe(false);
    });
  });

  describe('writeFile', () => {
    it('should write file successfully', () => {
      fs.writeFileSync.mockReturnValue(undefined);
      
      const result = writeFile('test.txt', 'content');
      expect(result).toBe(true);
      expect(fs.writeFileSync).toHaveBeenCalledWith('test.txt', 'content');
    });

    it('should handle write errors', () => {
      fs.writeFileSync.mockImplementation(() => {
        throw new Error('Write error');
      });
      
      const result = writeFile('error.txt', 'content');
      expect(result).toBe(false);
    });
  });

  describe('checkPackageExists', () => {
    it('should return true for existing packages', async () => {
      shell.exec.mockReturnValue({ code: 0 });
      
      const result = await checkPackageExists('express');
      expect(result).toBe(true);
      expect(shell.exec).toHaveBeenCalledWith('npm view express version', { silent: true });
    });

    it('should return false for non-existing packages', async () => {
      shell.exec.mockReturnValue({ code: 1 });
      
      const result = await checkPackageExists('non-existent-package');
      expect(result).toBe(false);
    });

    it('should handle exceptions', async () => {
      shell.exec.mockImplementation(() => {
        throw new Error('Network error');
      });
      
      const result = await checkPackageExists('error-package');
      expect(result).toBe(false);
    });
  });

  describe('getLatestVersion', () => {
    it('should return version for existing packages', async () => {
      shell.exec.mockReturnValue({ code: 0, stdout: '4.18.2\n' });
      
      const result = await getLatestVersion('express');
      expect(result).toBe('4.18.2');
    });

    it('should return null for non-existing packages', async () => {
      shell.exec.mockReturnValue({ code: 1 });
      
      const result = await getLatestVersion('non-existent-package');
      expect(result).toBeNull();
    });

    it('should handle exceptions', async () => {
      shell.exec.mockImplementation(() => {
        throw new Error('Network error');
      });
      
      const result = await getLatestVersion('error-package');
      expect(result).toBeNull();
    });
  });
});
