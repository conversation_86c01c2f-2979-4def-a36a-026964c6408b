/**
 * Pre-configured project templates for common use cases
 */

export const projectTemplates = {
  ecommerce: {
    name: "E-commerce Platform",
    description: "Complete e-commerce solution with product management, cart, and payments",
    features: [
      "Product catalog with categories",
      "Shopping cart functionality", 
      "User authentication & profiles",
      "Order management system",
      "Payment integration ready",
      "Admin dashboard",
      "Responsive design"
    ],
    backend: {
      additionalModels: ["Product", "Category", "Order", "Cart"],
      additionalRoutes: ["products", "categories", "orders", "cart"],
      requiredPackages: ["stripe", "multer", "sharp"],
      optionalPackages: ["nodemailer", "express-rate-limit"]
    },
    frontend: {
      additionalComponents: ["ProductCard", "ShoppingCart", "Checkout", "UserProfile"],
      styling: "Tailwind CSS",
      additionalPackages: ["@stripe/stripe-js", "react-router-dom", "axios"]
    }
  },

  blog: {
    name: "Blog Platform",
    description: "Modern blog platform with content management and SEO features",
    features: [
      "Rich text editor for posts",
      "Categories and tags system",
      "Comment system",
      "SEO optimization",
      "Social media sharing",
      "Author profiles",
      "Search functionality"
    ],
    backend: {
      additionalModels: ["Post", "Category", "Tag", "Comment"],
      additionalRoutes: ["posts", "categories", "tags", "comments"],
      requiredPackages: ["slugify", "marked"],
      optionalPackages: ["nodemailer", "express-rate-limit", "helmet"]
    },
    frontend: {
      additionalComponents: ["PostEditor", "PostCard", "CommentSection", "SearchBar"],
      styling: "Bootstrap",
      additionalPackages: ["react-quill", "react-router-dom", "axios", "react-helmet"]
    }
  },

  dashboard: {
    name: "Admin Dashboard",
    description: "Professional admin dashboard with analytics and data management",
    features: [
      "Interactive charts and graphs",
      "Data tables with sorting/filtering",
      "User management system",
      "Real-time notifications",
      "Export functionality",
      "Role-based permissions",
      "Dark/Light theme toggle"
    ],
    backend: {
      additionalModels: ["Analytics", "Notification", "Role", "Permission"],
      additionalRoutes: ["analytics", "notifications", "roles", "permissions"],
      requiredPackages: ["socket.io", "csv-parser", "excel4node"],
      optionalPackages: ["redis", "bull", "express-rate-limit"]
    },
    frontend: {
      additionalComponents: ["Chart", "DataTable", "NotificationCenter", "ThemeToggle"],
      styling: "Tailwind CSS",
      additionalPackages: ["chart.js", "react-chartjs-2", "socket.io-client", "react-table"]
    }
  },

  portfolio: {
    name: "Portfolio Website",
    description: "Professional portfolio website for developers and designers",
    features: [
      "Project showcase gallery",
      "Skills and experience section",
      "Contact form with validation",
      "Blog integration",
      "Resume/CV download",
      "Social media links",
      "Mobile responsive design"
    ],
    backend: {
      additionalModels: ["Project", "Skill", "Experience", "Contact"],
      additionalRoutes: ["projects", "skills", "experience", "contact"],
      requiredPackages: ["nodemailer", "multer"],
      optionalPackages: ["sharp", "express-rate-limit"]
    },
    frontend: {
      additionalComponents: ["ProjectGallery", "SkillsChart", "ContactForm", "Timeline"],
      styling: "Sass",
      additionalPackages: ["framer-motion", "react-router-dom", "axios"]
    }
  },

  social: {
    name: "Social Media Platform",
    description: "Social networking platform with posts, messaging, and user interactions",
    features: [
      "User profiles and following system",
      "Post creation with media upload",
      "Real-time messaging",
      "Like and comment system",
      "News feed algorithm",
      "Notification system",
      "Privacy controls"
    ],
    backend: {
      additionalModels: ["Post", "Follow", "Message", "Like", "Notification"],
      additionalRoutes: ["posts", "follows", "messages", "likes", "notifications"],
      requiredPackages: ["socket.io", "multer", "sharp"],
      optionalPackages: ["redis", "bull", "express-rate-limit", "helmet"]
    },
    frontend: {
      additionalComponents: ["PostFeed", "MessageCenter", "UserProfile", "NotificationBell"],
      styling: "Tailwind CSS",
      additionalPackages: ["socket.io-client", "react-router-dom", "axios", "react-infinite-scroll-component"]
    }
  },

  learning: {
    name: "Learning Management System",
    description: "Educational platform with courses, quizzes, and progress tracking",
    features: [
      "Course creation and management",
      "Video lessons with progress tracking",
      "Quiz and assignment system",
      "Student progress analytics",
      "Discussion forums",
      "Certificate generation",
      "Payment integration for courses"
    ],
    backend: {
      additionalModels: ["Course", "Lesson", "Quiz", "Enrollment", "Progress"],
      additionalRoutes: ["courses", "lessons", "quizzes", "enrollments", "progress"],
      requiredPackages: ["multer", "sharp", "pdfkit"],
      optionalPackages: ["stripe", "nodemailer", "express-rate-limit"]
    },
    frontend: {
      additionalComponents: ["CoursePlayer", "QuizInterface", "ProgressTracker", "DiscussionForum"],
      styling: "Bootstrap",
      additionalPackages: ["react-player", "react-router-dom", "axios", "react-pdf"]
    }
  }
};

export const getTemplateByName = (templateName) => {
  return projectTemplates[templateName] || null;
};

export const getAllTemplateNames = () => {
  return Object.keys(projectTemplates);
};

export const getTemplateChoices = () => {
  return Object.entries(projectTemplates).map(([key, template]) => ({
    name: `${template.name} - ${template.description}`,
    value: key,
    short: template.name
  }));
};

// Template-specific file generators
export const generateTemplateFiles = (templateName, projectName) => {
  const template = getTemplateByName(templateName);
  if (!template) return {};

  const files = {};

  // Generate README with template-specific content
  files['README.md'] = generateTemplateReadme(template, projectName);

  // Generate additional model files
  if (template.backend.additionalModels) {
    template.backend.additionalModels.forEach(model => {
      files[`server/models/${model.toLowerCase()}Model.js`] = generateModelFile(model);
    });
  }

  // Generate additional route files
  if (template.backend.additionalRoutes) {
    template.backend.additionalRoutes.forEach(route => {
      files[`server/routes/${route}Routes.js`] = generateRouteFile(route);
      files[`server/controllers/${route}Controller.js`] = generateControllerFile(route);
    });
  }

  return files;
};

const generateTemplateReadme = (template, projectName) => {
  return `# ${projectName}

${template.description}

## 🚀 Features

${template.features.map(feature => `- ✅ ${feature}`).join('\n')}

## 🛠️ Technology Stack

### Backend
- Node.js & Express.js
- MongoDB with Mongoose
- JWT Authentication
${template.backend.requiredPackages ? `- Additional packages: ${template.backend.requiredPackages.join(', ')}` : ''}

### Frontend
- React.js with Vite
- ${template.frontend.styling}
${template.frontend.additionalPackages ? `- Additional packages: ${template.frontend.additionalPackages.join(', ')}` : ''}

## 📋 Quick Start

1. Install dependencies:
\`\`\`bash
npm run install-all
\`\`\`

2. Set up environment variables:
\`\`\`bash
cp server/.env.example server/.env
# Edit server/.env with your configuration
\`\`\`

3. Start development servers:
\`\`\`bash
npm run dev
\`\`\`

## 📁 Project Structure

This project follows the MERN stack architecture with additional components for ${template.name.toLowerCase()}.

Built with ❤️ using StackWizard
`;
};

const generateModelFile = (modelName) => {
  const modelNameLower = modelName.toLowerCase();
  return `import mongoose from "mongoose";

const ${modelNameLower}Schema = new mongoose.Schema(
  {
    // Add your ${modelName} fields here
    name: {
      type: String,
      required: [true, "${modelName} name is required"],
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

const ${modelName} = mongoose.model("${modelName}", ${modelNameLower}Schema);
export default ${modelName};`;
};

const generateRouteFile = (routeName) => {
  const controllerName = `${routeName}Controller`;
  return `import express from "express";
import {
  get${routeName.charAt(0).toUpperCase() + routeName.slice(1)},
  create${routeName.charAt(0).toUpperCase() + routeName.slice(1)},
  update${routeName.charAt(0).toUpperCase() + routeName.slice(1)},
  delete${routeName.charAt(0).toUpperCase() + routeName.slice(1)}
} from "../controllers/${controllerName}.js";

const router = express.Router();

router.route("/")
  .get(get${routeName.charAt(0).toUpperCase() + routeName.slice(1)})
  .post(create${routeName.charAt(0).toUpperCase() + routeName.slice(1)});

router.route("/:id")
  .put(update${routeName.charAt(0).toUpperCase() + routeName.slice(1)})
  .delete(delete${routeName.charAt(0).toUpperCase() + routeName.slice(1)});

export default router;`;
};

const generateControllerFile = (routeName) => {
  const modelName = routeName.charAt(0).toUpperCase() + routeName.slice(1, -1); // Remove 's' from plural
  return `// @desc    Get all ${routeName}
// @route   GET /api/${routeName}
// @access  Public
export const get${routeName.charAt(0).toUpperCase() + routeName.slice(1)} = async (req, res) => {
  try {
    // Implement get all ${routeName} logic
    res.status(200).json({
      success: true,
      message: "Get all ${routeName} - Implementation needed"
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server Error",
      error: error.message
    });
  }
};

// @desc    Create new ${routeName.slice(0, -1)}
// @route   POST /api/${routeName}
// @access  Public
export const create${routeName.charAt(0).toUpperCase() + routeName.slice(1)} = async (req, res) => {
  try {
    // Implement create ${routeName.slice(0, -1)} logic
    res.status(201).json({
      success: true,
      message: "Create ${routeName.slice(0, -1)} - Implementation needed"
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: "Failed to create ${routeName.slice(0, -1)}",
      error: error.message
    });
  }
};

// @desc    Update ${routeName.slice(0, -1)}
// @route   PUT /api/${routeName}/:id
// @access  Public
export const update${routeName.charAt(0).toUpperCase() + routeName.slice(1)} = async (req, res) => {
  try {
    // Implement update ${routeName.slice(0, -1)} logic
    res.status(200).json({
      success: true,
      message: "Update ${routeName.slice(0, -1)} - Implementation needed"
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: "Failed to update ${routeName.slice(0, -1)}",
      error: error.message
    });
  }
};

// @desc    Delete ${routeName.slice(0, -1)}
// @route   DELETE /api/${routeName}/:id
// @access  Public
export const delete${routeName.charAt(0).toUpperCase() + routeName.slice(1)} = async (req, res) => {
  try {
    // Implement delete ${routeName.slice(0, -1)} logic
    res.status(200).json({
      success: true,
      message: "Delete ${routeName.slice(0, -1)} - Implementation needed"
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: "Failed to delete ${routeName.slice(0, -1)}",
      error: error.message
    });
  }
};`;
};
