/**
 * Configuration templates for different project types
 */

export const databaseTemplates = {
  mongodb: {
    local: "mongodb://localhost:27017",
    atlas: "mongodb+srv://<username>:<password>@<cluster>.mongodb.net",
    docker: "mongodb://mongo:27017"
  },
  postgresql: {
    local: "postgresql://username:password@localhost:5432/database",
    docker: "********************************************/database"
  },
  mysql: {
    local: "mysql://username:password@localhost:3306/database",
    docker: "mysql://username:password@mysql:3306/database"
  }
};

export const portTemplates = {
  development: {
    backend: 5000,
    frontend: 3000,
    database: 27017
  },
  production: {
    backend: process.env.PORT || 8080,
    frontend: 80,
    database: 27017
  }
};

export const environmentTemplates = {
  development: {
    NODE_ENV: "development",
    DEBUG: "true",
    LOG_LEVEL: "debug"
  },
  production: {
    NODE_ENV: "production",
    DEBUG: "false",
    LOG_LEVEL: "error"
  },
  testing: {
    NODE_ENV: "test",
    DEBUG: "true",
    LOG_LEVEL: "silent"
  }
};

export const securityTemplates = {
  jwt: {
    secret: "your-super-secret-jwt-key-change-this-in-production",
    expire: "30d",
    algorithm: "HS256"
  },
  cors: {
    origin: ["http://localhost:3000", "http://localhost:3001"],
    credentials: true,
    optionsSuccessStatus: 200
  },
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: "Too many requests from this IP, please try again later."
  }
};

export const dockerTemplates = {
  backend: `FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 5000

USER node

CMD ["npm", "start"]`,

  frontend: `FROM node:18-alpine as build

WORKDIR /app

COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]`,

  compose: `version: '3.8'

services:
  backend:
    build: ./server
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - MONGO_URI=mongodb://mongo:27017/app
    depends_on:
      - mongo
    networks:
      - app-network

  frontend:
    build: ./client
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - app-network

  mongo:
    image: mongo:6
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db
    networks:
      - app-network

volumes:
  mongo-data:

networks:
  app-network:
    driver: bridge`
};

export const cicdTemplates = {
  github: `name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js \${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: \${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Run linting
      run: npm run lint

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to production
      run: echo "Deploy to production server"`,

  gitlab: `stages:
  - test
  - build
  - deploy

variables:
  NODE_VERSION: "18"

test:
  stage: test
  image: node:\${NODE_VERSION}
  script:
    - npm ci
    - npm test
    - npm run lint
  coverage: '/Lines\\s*:\\s*(\\d+\\.?\\d*)%/'

build:
  stage: build
  image: node:\${NODE_VERSION}
  script:
    - npm ci
    - npm run build
  artifacts:
    paths:
      - dist/
    expire_in: 1 hour

deploy:
  stage: deploy
  script:
    - echo "Deploy to production"
  only:
    - main`
};

export const readmeTemplate = (projectName, stack, features) => `# ${projectName}

${projectName} is a modern ${stack} application built with StackWizard.

## 🚀 Features

${features.map(feature => `- ✅ ${feature}`).join('\n')}

## 📋 Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- MongoDB (for MERN stack)

## 🛠️ Installation

1. Clone the repository:
\`\`\`bash
git clone <repository-url>
cd ${projectName}
\`\`\`

2. Install dependencies:
\`\`\`bash
npm run install-all
\`\`\`

3. Set up environment variables:
\`\`\`bash
cp server/.env.example server/.env
# Edit server/.env with your configuration
\`\`\`

4. Start the development servers:
\`\`\`bash
npm run dev
\`\`\`

## 📁 Project Structure

\`\`\`
${projectName}/
├── server/          # Backend API
│   ├── config/      # Database and app configuration
│   ├── controllers/ # Route controllers
│   ├── models/      # Database models
│   ├── routes/      # API routes
│   ├── middleware/  # Custom middleware
│   └── server.js    # Express server
├── client/          # Frontend application
│   ├── src/         # Source files
│   ├── public/      # Static assets
│   └── package.json # Frontend dependencies
└── package.json     # Workspace configuration
\`\`\`

## 🔧 Available Scripts

- \`npm run dev\` - Start both frontend and backend in development mode
- \`npm run server\` - Start only the backend server
- \`npm run client\` - Start only the frontend application
- \`npm test\` - Run tests
- \`npm run lint\` - Run ESLint

## 🌐 API Endpoints

- \`GET /\` - Health check
- \`GET /api/users\` - Get all users
- \`POST /api/users\` - Create a new user
- \`GET /api/users/:id\` - Get user by ID

## 🚀 Deployment

### Using Docker

1. Build and run with Docker Compose:
\`\`\`bash
docker-compose up --build
\`\`\`

### Manual Deployment

1. Build the frontend:
\`\`\`bash
cd client && npm run build
\`\`\`

2. Start the backend in production mode:
\`\`\`bash
cd server && npm start
\`\`\`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

If you have any questions or need help, please open an issue or contact the development team.

---

Built with ❤️ using [StackWizard](https://github.com/bhuvi819381/StackWizard)
`;
