import chalk from "chalk";
import shell from "shelljs";
import ora from "ora";
import fs from "fs";
import path from "path";

/**
 * Validates project name according to npm naming conventions
 * @param {string} projectName - The project name to validate
 * @returns {object} - {isValid: boolean, error: string}
 */
export function validateProjectName(projectName) {
  if (!projectName || projectName.trim() === "") {
    return { isValid: false, error: "Project name cannot be empty" };
  }

  // Check for npm naming conventions
  const npmNameRegex = /^[a-z0-9]([a-z0-9\-_])*$/;
  if (!npmNameRegex.test(projectName)) {
    return {
      isValid: false,
      error: "Project name must contain only lowercase letters, numbers, hyphens, and underscores, and start with a letter or number"
    };
  }

  // Check length
  if (projectName.length > 214) {
    return { isValid: false, error: "Project name must be less than 214 characters" };
  }

  // Check for reserved names
  const reservedNames = ["node_modules", "favicon.ico", "package.json", "package-lock.json"];
  if (reservedNames.includes(projectName.toLowerCase())) {
    return { isValid: false, error: `"${projectName}" is a reserved name` };
  }

  // Check if directory already exists
  if (fs.existsSync(projectName)) {
    return { isValid: false, error: `Directory "${projectName}" already exists` };
  }

  return { isValid: true, error: null };
}

/**
 * Checks if required tools are installed
 * @returns {object} - {isValid: boolean, missing: string[]}
 */
export function checkPrerequisites() {
  const requiredTools = [
    { name: "node", command: "node --version" },
    { name: "npm", command: "npm --version" }
  ];

  const missing = [];

  for (const tool of requiredTools) {
    if (!shell.which(tool.name)) {
      missing.push(tool.name);
    }
  }

  return {
    isValid: missing.length === 0,
    missing
  };
}

/**
 * Creates a progress spinner with custom message
 * @param {string} message - The message to display
 * @returns {object} - ora spinner instance
 */
export function createProgressSpinner(message) {
  return ora({
    text: message,
    spinner: "dots",
    color: "cyan"
  });
}

/**
 * Handles errors gracefully with proper logging
 * @param {Error|string} error - The error to handle
 * @param {string} context - Context where the error occurred
 * @param {boolean} exit - Whether to exit the process
 */
export function handleError(error, context = "", exit = true) {
  console.log("\n");
  console.error(chalk.red("❌ Error occurred:"));
  
  if (context) {
    console.error(chalk.yellow(`Context: ${context}`));
  }
  
  if (error instanceof Error) {
    console.error(chalk.red(`Message: ${error.message}`));
    if (process.env.DEBUG) {
      console.error(chalk.gray(`Stack: ${error.stack}`));
    }
  } else {
    console.error(chalk.red(`Message: ${error}`));
  }
  
  console.log(chalk.gray("\nTip: Run with DEBUG=1 for more detailed error information"));
  
  if (exit) {
    process.exit(1);
  }
}

/**
 * Executes shell command with error handling and progress indication
 * @param {string} command - Command to execute
 * @param {string} description - Description for progress spinner
 * @param {boolean} silent - Whether to suppress output
 * @returns {Promise<boolean>} - Success status
 */
export async function executeCommand(command, description, silent = true) {
  const spinner = createProgressSpinner(description);
  spinner.start();

  try {
    const result = shell.exec(command, { silent });
    
    if (result.code !== 0) {
      spinner.fail(chalk.red(`Failed: ${description}`));
      console.error(chalk.red(`Command failed: ${command}`));
      console.error(chalk.red(`Error: ${result.stderr}`));
      return false;
    }
    
    spinner.succeed(chalk.green(`Completed: ${description}`));
    return true;
  } catch (error) {
    spinner.fail(chalk.red(`Failed: ${description}`));
    handleError(error, `Executing command: ${command}`, false);
    return false;
  }
}

/**
 * Validates and prompts for project name with retry logic
 * @param {string} defaultName - Default project name
 * @param {string} message - Prompt message
 * @returns {Promise<string>} - Valid project name
 */
export async function promptProjectName(defaultName, message) {
  while (true) {
    const { projectName } = await inquirer.prompt([
      {
        type: "input",
        name: "projectName",
        message,
        default: defaultName,
        validate: (input) => {
          const validation = validateProjectName(input);
          return validation.isValid || validation.error;
        }
      }
    ]);

    const validation = validateProjectName(projectName);
    if (validation.isValid) {
      return projectName;
    }

    console.log(chalk.red(`❌ ${validation.error}`));
    console.log(chalk.yellow("Please try a different name.\n"));
  }
}

/**
 * Creates directory with error handling
 * @param {string} dirPath - Directory path to create
 * @returns {boolean} - Success status
 */
export function createDirectory(dirPath) {
  try {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(chalk.green(`✅ Created directory: ${dirPath}`));
      return true;
    }
    return true;
  } catch (error) {
    handleError(error, `Creating directory: ${dirPath}`, false);
    return false;
  }
}

/**
 * Writes file with error handling
 * @param {string} filePath - File path
 * @param {string} content - File content
 * @returns {boolean} - Success status
 */
export function writeFile(filePath, content) {
  try {
    fs.writeFileSync(filePath, content);
    console.log(chalk.green(`✅ Created file: ${filePath}`));
    return true;
  } catch (error) {
    handleError(error, `Writing file: ${filePath}`, false);
    return false;
  }
}

/**
 * Checks if a package is available in npm registry
 * @param {string} packageName - Package name to check
 * @returns {Promise<boolean>} - Whether package exists
 */
export async function checkPackageExists(packageName) {
  try {
    const result = shell.exec(`npm view ${packageName} version`, { silent: true });
    return result.code === 0;
  } catch (error) {
    return false;
  }
}

/**
 * Gets the latest version of a package
 * @param {string} packageName - Package name
 * @returns {Promise<string|null>} - Latest version or null
 */
export async function getLatestVersion(packageName) {
  try {
    const result = shell.exec(`npm view ${packageName} version`, { silent: true });
    if (result.code === 0) {
      return result.stdout.trim();
    }
    return null;
  } catch (error) {
    return null;
  }
}
