import fs from "fs";
import inquirer from "inquirer";
import shell from "shelljs";
import chalk from "chalk";
import {
  executeCommand,
  handleError,
  writeFile,
  createDirectory,
} from "../../utils/helpers.js";

async function setupFrontend() {
  try {
    console.log(chalk.yellow("🎨 Setting up frontend...\n"));

    // Create client directory
    if (!createDirectory("client")) {
      throw new Error("Failed to create client directory");
    }

    const originalDir = process.cwd();
    shell.cd("client");

    try {
      // Ask for React or TypeScript
      const { framework } = await inquirer.prompt([
        {
          type: "list",
          name: "framework",
          message: "Choose a frontend setup:",
          choices: [
            { name: "⚛️ React.js", value: "React.js" },
            { name: "⚛️ React + TypeScript", value: "React + TypeScript" },
          ],
        },
      ]);

      const template =
        framework === "React + TypeScript" ? "react-ts" : "react";

      // Create Vite project
      const viteSuccess = await executeCommand(
        `npm create vite@latest . -- --template ${template}`,
        `Creating Vite project with ${framework}`
      );

      if (!viteSuccess) {
        throw new Error(`Failed to create Vite project with ${framework}`);
      }

      // Install dependencies
      const installSuccess = await executeCommand(
        "npm install",
        "Installing frontend dependencies"
      );

      if (!installSuccess) {
        throw new Error("Failed to install frontend dependencies");
      }

      // Ask for styling framework
      const { styling } = await inquirer.prompt([
        {
          type: "list",
          name: "styling",
          message: "Choose a styling framework:",
          choices: [
            { name: "🎨 Tailwind CSS", value: "Tailwind CSS" },
            { name: "🅱️ Bootstrap", value: "Bootstrap" },
            { name: "💅 Sass", value: "Sass" },
            { name: "🚫 None", value: "None" },
          ],
        },
      ]);

      // Install styling framework
      let stylingSuccess = true;
      if (styling === "Tailwind CSS") {
        stylingSuccess = await executeCommand(
          "npm install -D tailwindcss postcss autoprefixer && npx tailwindcss init -p",
          "Installing and configuring Tailwind CSS"
        );

        if (stylingSuccess) {
          // Create Tailwind config
          const tailwindConfig = `/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}`;
          writeFile("tailwind.config.js", tailwindConfig);

          // Update CSS file
          const tailwindCSS = `@tailwind base;
@tailwind components;
@tailwind utilities;`;
          writeFile("src/index.css", tailwindCSS);
        }
      } else if (styling === "Bootstrap") {
        stylingSuccess = await executeCommand(
          "npm install bootstrap@5.3.3",
          "Installing Bootstrap"
        );
      } else if (styling === "Sass") {
        stylingSuccess = await executeCommand(
          "npm install -D sass",
          "Installing Sass"
        );
      }

      if (!stylingSuccess && styling !== "None") {
        console.log(
          chalk.yellow(
            `⚠️ Failed to install ${styling}, continuing without it...`
          )
        );
      }

      // Create enhanced App component
      const appFile =
        framework === "React + TypeScript" ? "src/App.tsx" : "src/App.jsx";
      console.log(chalk.blue("⚛️ Creating enhanced App component..."));

      const isTypeScript = framework === "React + TypeScript";
      const appContent = `${isTypeScript ? 'import React from "react";' : ""}
import './App.css'

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>🚀 Welcome to Your Project!</h1>
        <p>Built with StackWizard</p>
        <div className="features">
          <h2>✨ Features Included:</h2>
          <ul>
            <li>⚛️ ${framework}</li>
            <li>⚡ Vite for fast development</li>
            ${styling !== "None" ? `<li>🎨 ${styling} for styling</li>` : ""}
            <li>🔧 Ready for development</li>
          </ul>
        </div>
        <p className="get-started">
          Edit <code>src/App.${
            isTypeScript ? "tsx" : "jsx"
          }</code> and save to reload.
        </p>
      </header>
    </div>
  )
}

export default App`;

      if (!writeFile(appFile, appContent)) {
        console.log(
          chalk.yellow(
            "⚠️ Failed to create enhanced App component, using default..."
          )
        );
        fs.writeFileSync(
          appFile,
          "export default function App() { return <h1>Welcome to the project.</h1>; }"
        );
      }

      // Create enhanced CSS
      const appCSS = `#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.App-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
  border-radius: 1rem;
  color: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.App-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.features {
  margin: 2rem 0;
  text-align: left;
}

.features ul {
  list-style: none;
  padding: 0;
}

.features li {
  padding: 0.5rem 0;
  font-size: 1.1rem;
}

.get-started {
  margin-top: 2rem;
  font-size: 1rem;
  opacity: 0.9;
}

code {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.2rem 0.5rem;
  border-radius: 0.3rem;
  font-family: 'Courier New', monospace;
}`;

      writeFile("src/App.css", appCSS);

      console.log(chalk.green("\n✅ Frontend setup completed! 🚀"));
      console.log(chalk.cyan("📋 Frontend features included:"));
      console.log(chalk.white(`   • ${framework} with Vite`));
      console.log(chalk.white("   • Enhanced App component"));
      console.log(chalk.white("   • Modern CSS styling"));
      if (styling !== "None") {
        console.log(chalk.white(`   • ${styling} integration`));
      }
    } catch (error) {
      shell.cd(originalDir);
      throw error;
    }

    shell.cd("../../");
  } catch (error) {
    handleError(error, "Frontend setup", false);
    throw error;
  }
}

export default setupFrontend;
