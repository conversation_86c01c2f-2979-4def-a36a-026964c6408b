import fs from "fs";
import inquirer from "inquirer";
import chalk from "chalk";
import shell from "shelljs";
import {
  executeCommand,
  handleError,
  writeFile,
  createDirectory,
} from "../../utils/helpers.js";
import {
  databaseTemplates,
  environmentTemplates,
  securityTemplates,
} from "../../config/templates.js";

async function setupBackend() {
  try {
    console.log(chalk.yellow("🔧 Setting up backend...\n"));

    // Create server directory
    if (!createDirectory("server")) {
      throw new Error("Failed to create server directory");
    }

    const originalDir = process.cwd();
    shell.cd("server");

    try {
      // Initialize package.json
      const initSuccess = await executeCommand(
        "npm init -y",
        "Initializing package.json"
      );

      if (!initSuccess) {
        throw new Error("Failed to initialize package.json");
      }

      // Modify package.json
      console.log(chalk.blue("📝 Configuring package.json..."));
      const packageJson = JSON.parse(fs.readFileSync("package.json"));
      packageJson.main = "server.js";
      packageJson.type = "module";
      packageJson.scripts = {
        dev: "nodemon server.js",
        start: "node server.js",
        test: 'echo "Error: no test specified" && exit 1',
      };
      packageJson.description = "Backend server for MERN stack application";
      packageJson.keywords = ["express", "mongodb", "nodejs", "api"];

      if (!writeFile("package.json", JSON.stringify(packageJson, null, 2))) {
        throw new Error("Failed to update package.json");
      }

      // Mandatory dependencies
      const mandatoryDeps = ["express", "mongoose", "dotenv", "cors"];
      const mandatoryInstallSuccess = await executeCommand(
        `npm install ${mandatoryDeps.join(" ")}`,
        `Installing mandatory dependencies: ${mandatoryDeps.join(", ")}`
      );

      if (!mandatoryInstallSuccess) {
        throw new Error("Failed to install mandatory dependencies");
      }

      // Ask for optional dependencies
      const optionalDeps = [
        { name: "jsonwebtoken", message: "🔐 Install JSON Web Token (JWT)?" },
        {
          name: "bcryptjs",
          message: "🔒 Install bcrypt for password hashing?",
        },
        { name: "multer", message: "📁 Install Multer for file uploads?" },
        { name: "cookie-parser", message: "🍪 Install Cookie Parser?" },
        { name: "compression", message: "📦 Install Compression middleware?" },
        { name: "helmet", message: "🛡️ Install Helmet for security?" },
        { name: "express-rate-limit", message: "⏱️ Install Rate Limiting?" },
      ];

      const selectedDeps = [];
      console.log(chalk.cyan("\n🔧 Optional Dependencies:"));

      for (const dep of optionalDeps) {
        const { install } = await inquirer.prompt([
          {
            type: "confirm",
            name: "install",
            message: dep.message,
            default: false,
          },
        ]);
        if (install) {
          selectedDeps.push(dep.name);
        }
      }

      // Install selected optional dependencies
      if (selectedDeps.length > 0) {
        const optionalInstallSuccess = await executeCommand(
          `npm install ${selectedDeps.join(" ")}`,
          `Installing optional dependencies: ${selectedDeps.join(", ")}`
        );

        if (!optionalInstallSuccess) {
          console.log(
            chalk.yellow(
              "⚠️ Some optional dependencies failed to install, continuing..."
            )
          );
        }
      }

      // Install dev dependencies
      const devInstallSuccess = await executeCommand(
        "npm install --save-dev nodemon",
        "Installing dev dependencies: nodemon"
      );

      if (!devInstallSuccess) {
        throw new Error("Failed to install dev dependencies");
      }

      // Ask for configuration preferences
      console.log(chalk.cyan("\n🔧 Configuration Options:"));

      const { environment, database, customPort } = await inquirer.prompt([
        {
          type: "list",
          name: "environment",
          message: "Choose your deployment environment:",
          choices: [
            { name: "🔧 Development", value: "development" },
            { name: "🚀 Production", value: "production" },
            { name: "🧪 Testing", value: "testing" },
          ],
          default: "development",
        },
        {
          type: "list",
          name: "database",
          message: "Choose your database setup:",
          choices: [
            { name: "🏠 Local MongoDB", value: "local" },
            { name: "☁️ MongoDB Atlas", value: "atlas" },
            { name: "🐳 Docker MongoDB", value: "docker" },
          ],
          default: "local",
        },
        {
          type: "input",
          name: "customPort",
          message: "Enter custom port (or press Enter for default 5000):",
          default: "5000",
          validate: (input) => {
            const port = parseInt(input);
            if (isNaN(port) || port < 1000 || port > 65535) {
              return "Please enter a valid port number between 1000 and 65535";
            }
            return true;
          },
        },
      ]);

      // Create necessary folders
      const folders = [
        "config",
        "routes",
        "controllers",
        "models",
        "middleware",
      ];
      console.log(chalk.blue("📁 Creating project structure..."));

      for (const folder of folders) {
        if (!createDirectory(folder)) {
          throw new Error(`Failed to create ${folder} directory`);
        }
      }

      // Create config/db.js
      console.log(chalk.blue("🗄️ Creating database configuration..."));
      const dbConfig = `import mongoose from "mongoose";

const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log(\`✅ MongoDB Connected: \${conn.connection.host}\`);
  } catch (error) {
    console.error(\`❌ Database connection error: \${error.message}\`);
    process.exit(1);
  }
};

export default connectDB;`;

      if (!writeFile("config/db.js", dbConfig)) {
        throw new Error("Failed to create database configuration");
      }

      // Create models/userModel.js
      console.log(chalk.blue("👤 Creating user model..."));
      const userModel = `import mongoose from "mongoose";

const userSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Name is required"],
      trim: true,
      maxlength: [50, "Name cannot exceed 50 characters"]
    },
    email: {
      type: String,
      required: [true, "Email is required"],
      unique: true,
      lowercase: true,
      match: [/^\\w+([\\.-]?\\w+)*@\\w+([\\.-]?\\w+)*(\\.\\w{2,3})+$/, "Please enter a valid email"]
    },
    password: {
      type: String,
      required: [true, "Password is required"],
      minlength: [6, "Password must be at least 6 characters"]
    },
    role: {
      type: String,
      enum: ["user", "admin"],
      default: "user"
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Index for better query performance
userSchema.index({ email: 1 });

const User = mongoose.model("User", userSchema);
export default User;`;

      if (!writeFile("models/userModel.js", userModel)) {
        throw new Error("Failed to create user model");
      }

      // Create controllers/userController.js
      console.log(chalk.blue("🎮 Creating user controller..."));
      const userController = `import User from "../models/userModel.js";

// @desc    Get all users
// @route   GET /api/users
// @access  Public
export const getUsers = async (req, res) => {
  try {
    const users = await User.find({}).select("-password");
    res.status(200).json({
      success: true,
      count: users.length,
      data: users
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server Error",
      error: error.message
    });
  }
};

// @desc    Get single user
// @route   GET /api/users/:id
// @access  Public
export const getUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select("-password");

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found"
      });
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server Error",
      error: error.message
    });
  }
};

// @desc    Create new user
// @route   POST /api/users
// @access  Public
export const createUser = async (req, res) => {
  try {
    const { name, email, password } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: "User already exists with this email"
      });
    }

    const user = await User.create({
      name,
      email,
      password
    });

    res.status(201).json({
      success: true,
      data: {
        _id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        isActive: user.isActive
      }
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: "Failed to create user",
      error: error.message
    });
  }
};`;

      if (!writeFile("controllers/userController.js", userController)) {
        throw new Error("Failed to create user controller");
      }

      // Create routes/userRoutes.js
      console.log(chalk.blue("🛣️ Creating user routes..."));
      const userRoutes = `import express from "express";
import { getUsers, getUser, createUser } from "../controllers/userController.js";

const router = express.Router();

// User routes
router.route("/")
  .get(getUsers)
  .post(createUser);

router.route("/:id")
  .get(getUser);

export default router;`;

      if (!writeFile("routes/userRoutes.js", userRoutes)) {
        throw new Error("Failed to create user routes");
      }

      // Create middleware/authMiddleware.js only if JWT is installed
      if (selectedDeps.includes("jsonwebtoken")) {
        console.log(chalk.blue("🔐 Creating authentication middleware..."));
        const authMiddleware = `import jwt from "jsonwebtoken";
import User from "../models/userModel.js";

export const protect = async (req, res, next) => {
  let token;

  if (req.headers.authorization && req.headers.authorization.startsWith("Bearer")) {
    try {
      // Get token from header
      token = req.headers.authorization.split(" ")[1];

      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);

      // Get user from the token
      req.user = await User.findById(decoded.id).select("-password");

      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: "User not found"
        });
      }

      next();
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: "Not authorized, token failed"
      });
    }
  }

  if (!token) {
    return res.status(401).json({
      success: false,
      message: "Not authorized, no token"
    });
  }
};

export const authorize = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: \`User role \${req.user.role} is not authorized to access this route\`
      });
    }
    next();
  };
};`;

        if (!writeFile("middleware/authMiddleware.js", authMiddleware)) {
          throw new Error("Failed to create authentication middleware");
        }
      }

      // Create server.js
      console.log(chalk.blue("🚀 Creating server configuration..."));

      // Build server.js content based on selected dependencies
      let serverImports = `import express from "express";
import dotenv from "dotenv";
import cors from "cors";
import connectDB from "./config/db.js";
import userRoutes from "./routes/userRoutes.js";`;

      let middlewareSetup = `// Middleware
app.use(cors({
  origin: process.env.CLIENT_URL || "http://localhost:3000",
  credentials: true
}));

app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true }));`;

      // Add optional middleware based on selected dependencies
      if (selectedDeps.includes("helmet")) {
        serverImports += `\nimport helmet from "helmet";`;
        middlewareSetup += `\napp.use(helmet());`;
      }

      if (selectedDeps.includes("compression")) {
        serverImports += `\nimport compression from "compression";`;
        middlewareSetup += `\napp.use(compression());`;
      }

      if (selectedDeps.includes("cookie-parser")) {
        serverImports += `\nimport cookieParser from "cookie-parser";`;
        middlewareSetup += `\napp.use(cookieParser());`;
      }

      if (selectedDeps.includes("express-rate-limit")) {
        serverImports += `\nimport rateLimit from "express-rate-limit";`;
        middlewareSetup += `\n
// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    message: "Too many requests from this IP, please try again later."
  }
});
app.use("/api", limiter);`;
      }

      const serverContent = `${serverImports}

// Load environment variables
dotenv.config();

// Connect to database
connectDB();

const app = express();

${middlewareSetup}

// Routes
app.use("/api/users", userRoutes);

// Health check route
app.get("/", (req, res) => {
  res.status(200).json({
    success: true,
    message: "🚀 API is running successfully!",
    version: "1.0.0",
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use("*", (req, res) => {
  res.status(404).json({
    success: false,
    message: "Route not found"
  });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: "Something went wrong!",
    error: process.env.NODE_ENV === "development" ? err.message : {}
  });
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log(\`🚀 Server running in \${process.env.NODE_ENV || "development"} mode on port \${PORT}\`);
});`;

      if (!writeFile("server.js", serverContent)) {
        throw new Error("Failed to create server configuration");
      }

      // Create .env file
      console.log(chalk.blue("🔧 Creating environment configuration..."));

      // Get environment template and database URI
      const envTemplate = environmentTemplates[environment];
      const dbUri = databaseTemplates.mongodb[database];

      let envContent = `# Server Configuration
PORT=${customPort}
NODE_ENV=${envTemplate.NODE_ENV}
DEBUG=${envTemplate.DEBUG}
LOG_LEVEL=${envTemplate.LOG_LEVEL}

# Database Configuration
MONGO_URI=${dbUri}${database === "local" ? "/your-app-name" : ""}

# Frontend URL (for CORS)
CLIENT_URL=http://localhost:3000`;

      if (selectedDeps.includes("jsonwebtoken")) {
        const jwtConfig = securityTemplates.jwt;
        envContent += `\n\n# JWT Configuration
JWT_SECRET=${jwtConfig.secret}
JWT_EXPIRE=${jwtConfig.expire}
JWT_ALGORITHM=${jwtConfig.algorithm}`;
      }

      envContent += `\n\n# Add your other environment variables here
# API_KEY=your-api-key
# EMAIL_SERVICE=your-email-service`;

      if (!writeFile(".env", envContent)) {
        throw new Error("Failed to create environment configuration");
      }

      // Create .env.example file
      const envExampleContent = envContent.replace(/=.*/g, "=");
      if (!writeFile(".env.example", envExampleContent)) {
        console.log(chalk.yellow("⚠️ Failed to create .env.example file"));
      }

      console.log(chalk.green("\n✅ Backend setup completed! 🚀"));
      console.log(chalk.cyan("📋 Backend features included:"));
      console.log(chalk.white("   • Express.js server with error handling"));
      console.log(chalk.white("   • MongoDB connection with Mongoose"));
      console.log(chalk.white("   • User model with validation"));
      console.log(chalk.white("   • RESTful API routes"));
      console.log(chalk.white("   • Environment configuration"));

      if (selectedDeps.length > 0) {
        console.log(
          chalk.white(`   • Optional packages: ${selectedDeps.join(", ")}`)
        );
      }
    } catch (error) {
      shell.cd(originalDir);
      throw error;
    }

    shell.cd("..");
  } catch (error) {
    handleError(error, "Backend setup", false);
    throw error;
  }
}

export default setupBackend;
